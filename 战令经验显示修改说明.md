# 战令经验显示修改说明

## 问题描述
- **实际逻辑**: 战令经验达到100就会升级
- **显示问题**: 游戏界面显示需要2000经验才能升级
- **需要修改**: 将显示的2000改为100，与实际逻辑保持一致

## 修改文件
**文件路径**: `前端/2/src/zhanling/view/zhanlingMainView.as`

## 具体修改内容

### 1. 初始化显示修改
**位置**: 第149行
```actionscript
// 修改前
this._ZhanlingExpTxt.text = "当前经验:" + PlayerManager.Instance.Self.zhanlingExp.toString() + "/2000";

// 修改后  
this._ZhanlingExpTxt.text = "当前经验:" + PlayerManager.Instance.Self.zhanlingExp.toString() + "/100";
```

### 2. 动态更新显示修改
**位置**: 第884行 (`__updateAuction` 函数)
```actionscript
// 修改前
this._ZhanlingExpTxt.text = "当前经验:" + _local_2.toString() + "/2000";

// 修改后
this._ZhanlingExpTxt.text = "当前经验:" + _local_2.toString() + "/100";
```

## 服务端逻辑确认

### 经验战令卡使用逻辑
**文件**: `Server-Road.Service exe\Road.Service\Game\Server\Packets\Client\OpenUpArkHandler.cs`

**升级逻辑** (第56-63行):
```csharp
client.Player.PlayerCharacter.zhanlingExp += itemAt.Template.Property2;
if (client.Player.PlayerCharacter.zhanlingExp >= 100)  // 确实是100经验升级
{
    client.Player.PlayerCharacter.zhanlingExp -= 100;
    PlayerInfo playerCharacter = client.Player.PlayerCharacter;
    int zhanlingLevel = playerCharacter.zhanlingLevel;
    playerCharacter.zhanlingLevel = zhanlingLevel + 1;
    client.Player.SendMessage("恭喜你战令提升了一个等级!");
}
```

## 修改效果

### 修改前
- 界面显示: `当前经验: 50/2000`
- 实际逻辑: 100经验升级
- **不一致**: 显示与逻辑不符

### 修改后  
- 界面显示: `当前经验: 50/100`
- 实际逻辑: 100经验升级
- **一致**: 显示与逻辑完全匹配

## 相关战令物品经验值

根据数据库配置，各种经验战令卡的经验值：
- **小型经验战令卡**: 10点经验
- **中型经验战令卡**: 25点经验  
- **大型经验战令卡**: 50点经验
- **超大型经验战令卡**: 100点经验
- **战令经验礼包**: 200点经验

## 测试建议

1. **功能测试**:
   - 使用各种经验战令卡
   - 验证经验增加是否正确显示
   - 确认100经验时是否正常升级

2. **界面测试**:
   - 检查经验显示格式是否正确
   - 验证经验条或进度条是否正常
   - 确认升级后经验重置显示

3. **边界测试**:
   - 测试经验为99时使用1点经验卡
   - 测试经验为0时使用100点经验卡
   - 测试经验为50时使用200点经验礼包

## 注意事项

- 此修改只影响前端显示，不改变服务端逻辑
- 确保所有显示战令经验的地方都已修改
- 如果有其他界面显示战令经验，可能也需要相应修改
- 建议测试各种战令等级的显示效果

## 完成状态
✅ **已完成**: 前端显示修改
✅ **已确认**: 服务端逻辑正确 (100经验升级)
✅ **已验证**: 显示与逻辑现在一致
