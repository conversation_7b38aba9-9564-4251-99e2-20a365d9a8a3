package zhanling.view
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.zhanlingManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class zhanlingMainView extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var _InfoBg:Bitmap;
      
      private var _putongzhanling:Bitmap;
      
      private var _gaojizhanling:Bitmap;
      
      private var _chaojizhanling:Bitmap;
      
      private var _goldBg:MovieClip;
      
      private var Nowputongzhanling:int;
      
      private var Nowgaojizhanling:int;
      
      private var Nowchaojizhanling:int;
      
      private var _goldTxt:FilterFrameText;
      
      private var _ZhanlingExpTxt:FilterFrameText;
      
      private var _Zhanlingshenfen:FilterFrameText;
      
      private var _ZhanlingLevel:FilterFrameText;
      
      private var putongzhanlingCell1:zhanlingItemCell;
      
      private var putongzhanlingCell2:zhanlingItemCell;
      
      private var putongzhanlingCell3:zhanlingItemCell;
      
      private var putongzhanlingCell4:zhanlingItemCell;
      
      private var putongzhanlingCell5:zhanlingItemCell;
      
      private var putongzhanlingCell6:zhanlingItemCell;
      
      private var putongzhanlingCell7:zhanlingItemCell;
      
      private var putongzhanlingCell8:zhanlingItemCell;
      
      private var putongzhanlingCell9:zhanlingItemCell;
      
      private var putongzhanlingCell10:zhanlingItemCell;
      
      private var gaojizhanlingCell1:zhanlingItemCell;
      
      private var gaojizhanlingCell2:zhanlingItemCell;
      
      private var gaojizhanlingCell3:zhanlingItemCell;
      
      private var gaojizhanlingCell4:zhanlingItemCell;
      
      private var gaojizhanlingCell5:zhanlingItemCell;
      
      private var gaojizhanlingCell6:zhanlingItemCell;
      
      private var gaojizhanlingCell7:zhanlingItemCell;
      
      private var gaojizhanlingCell8:zhanlingItemCell;
      
      private var gaojizhanlingCell9:zhanlingItemCell;
      
      private var gaojizhanlingCell10:zhanlingItemCell;
      
      private var chaojizhanlingCell1:zhanlingItemCell;
      
      private var chaojizhanlingCell2:zhanlingItemCell;
      
      private var chaojizhanlingCell3:zhanlingItemCell;
      
      private var chaojizhanlingCell4:zhanlingItemCell;
      
      private var chaojizhanlingCell5:zhanlingItemCell;
      
      private var chaojizhanlingCell6:zhanlingItemCell;
      
      private var chaojizhanlingCell7:zhanlingItemCell;
      
      private var chaojizhanlingCell8:zhanlingItemCell;
      
      private var chaojizhanlingCell9:zhanlingItemCell;
      
      private var chaojizhanlingCell10:zhanlingItemCell;
      
      private var _putongleftBtn:SimpleBitmapButton;
      
      private var _putongrightBtn:SimpleBitmapButton;
      
      private var _gaojileftBtn:SimpleBitmapButton;
      
      private var _gaojirightBtn:SimpleBitmapButton;
      
      private var _chaojileftBtn:SimpleBitmapButton;
      
      private var _chaojirightBtn:SimpleBitmapButton;
      
      private var _ZhanlingIcon:SimpleBitmapButton;
      
      public function zhanlingMainView()
      {
         super();
         this.x = 0;
         this.y = 47;
         this.Nowputongzhanling = 1;
         this.Nowgaojizhanling = 1;
         this.Nowchaojizhanling = 1;
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         var s1:String = null;
         SocketManager.Instance.addEventListener("Update_Zhanling",this.__updateAuction);
         this._bg = ComponentFactory.Instance.creatBitmap("bg");
         this._InfoBg = ComponentFactory.Instance.creatBitmap("zhanling.MainInfoList");
         this._goldTxt = ComponentFactory.Instance.creatComponentByStylename("zhanling.rightView.goldTxt");
         this._goldTxt.text = "战令币:" + PlayerManager.Instance.Self.zhanling.toString();
         this._ZhanlingLevel = ComponentFactory.Instance.creatComponentByStylename("zhanling.rightView.ZhanlingLevelTxt");
         this._ZhanlingLevel.text = "当前等级:" + PlayerManager.Instance.Self.zhanlingLevel.toString();
         this._ZhanlingExpTxt = ComponentFactory.Instance.creatComponentByStylename("zhanling.ExpTxt");
         this._ZhanlingExpTxt.text = "当前经验:" + PlayerManager.Instance.Self.zhanlingExp.toString() + "/100";
         this._Zhanlingshenfen = ComponentFactory.Instance.creatComponentByStylename("zhanling.shenfenTxt");
         if(PlayerManager.Instance.Self.zhanlingVipType == 0)
         {
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 1)
         {
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 2)
         {
         }
         addChild(this._bg);
         addChild(this._InfoBg);
         addChild(this._goldTxt);
         addChild(this._ZhanlingExpTxt);
         addChild(this._Zhanlingshenfen);
         addChild(this._ZhanlingLevel);
         this._putongleftBtn = ComponentFactory.Instance.creatComponentByStylename("putongzhanlingBag.button.left");
         this._putongrightBtn = ComponentFactory.Instance.creatComponentByStylename("putongzhanlingBag.button.right");
         this._gaojileftBtn = ComponentFactory.Instance.creatComponentByStylename("gaojizhanlingBag.button.left");
         this._gaojirightBtn = ComponentFactory.Instance.creatComponentByStylename("gaojizhanlingBag.button.right");
         this._chaojileftBtn = ComponentFactory.Instance.creatComponentByStylename("chaojizhanlingBag.button.left");
         this._chaojirightBtn = ComponentFactory.Instance.creatComponentByStylename("chaojizhanlingBag.button.right");
         if(PlayerManager.Instance.Self.zhanlingVipType == 0)
         {
            s1 = "zhanling.chujizhanling.Icon";
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 1)
         {
            s1 = "zhanling.gaojizhanling.Icon";
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 2)
         {
            s1 = "zhanling.chaojizhanling.Icon";
         }
         this._ZhanlingIcon = ComponentFactory.Instance.creatComponentByStylename(s1);
         addChild(this._putongleftBtn);
         addChild(this._putongrightBtn);
         addChild(this._gaojileftBtn);
         addChild(this._gaojirightBtn);
         addChild(this._chaojileftBtn);
         addChild(this._chaojirightBtn);
         addChild(this._ZhanlingIcon);
         this.Refreshputongzhanling(1);
         this.Refreshgaojizhanling(1);
         this.Refreshchaojizhanling(1);
      }
      
      private function Refreshputongzhanling(FromSpot:int) : void
      {
         this.disposeputongzhanlingCell();
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot) != null)
         {
            this.putongzhanlingCell1 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot).TemplateID),1,this.Nowputongzhanling + 0,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 0).Count);
            PositionUtils.setPos(this.putongzhanlingCell1,"putongzhanling.pos1");
         }
         else if(this.putongzhanlingCell1 != null)
         {
            removeChild(this.putongzhanlingCell1);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 1) != null)
         {
            this.putongzhanlingCell2 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 1).TemplateID),1,this.Nowputongzhanling + 1,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 1).Count);
            PositionUtils.setPos(this.putongzhanlingCell2,"putongzhanling.pos2");
         }
         else if(this.putongzhanlingCell2 != null)
         {
            removeChild(this.putongzhanlingCell2);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 2) != null)
         {
            this.putongzhanlingCell3 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 2).TemplateID),1,this.Nowputongzhanling + 2,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 2).Count);
            PositionUtils.setPos(this.putongzhanlingCell3,"putongzhanling.pos3");
         }
         else if(this.putongzhanlingCell3 != null)
         {
            removeChild(this.putongzhanlingCell3);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 3) != null)
         {
            this.putongzhanlingCell4 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 3).TemplateID),1,this.Nowputongzhanling + 3,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 3).Count);
            PositionUtils.setPos(this.putongzhanlingCell4,"putongzhanling.pos4");
         }
         else if(this.putongzhanlingCell4 != null)
         {
            removeChild(this.putongzhanlingCell4);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 4) != null)
         {
            this.putongzhanlingCell5 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 4).TemplateID),1,this.Nowputongzhanling + 4,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 4).Count);
            PositionUtils.setPos(this.putongzhanlingCell5,"putongzhanling.pos5");
         }
         else if(this.putongzhanlingCell5 != null)
         {
            removeChild(this.putongzhanlingCell5);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 5) != null)
         {
            this.putongzhanlingCell6 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 5).TemplateID),1,this.Nowputongzhanling + 5,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 5).Count);
            PositionUtils.setPos(this.putongzhanlingCell6,"putongzhanling.pos6");
         }
         else if(this.putongzhanlingCell6 != null)
         {
            removeChild(this.putongzhanlingCell6);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 6) != null)
         {
            this.putongzhanlingCell7 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 6).TemplateID),1,this.Nowputongzhanling + 6,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 6).Count);
            PositionUtils.setPos(this.putongzhanlingCell7,"putongzhanling.pos7");
         }
         else if(this.putongzhanlingCell7 != null)
         {
            removeChild(this.putongzhanlingCell7);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 7) != null)
         {
            this.putongzhanlingCell8 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 7).TemplateID),1,this.Nowputongzhanling + 7,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 7).Count);
            PositionUtils.setPos(this.putongzhanlingCell8,"putongzhanling.pos8");
         }
         else if(this.putongzhanlingCell8 != null)
         {
            removeChild(this.putongzhanlingCell8);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 8) != null)
         {
            this.putongzhanlingCell9 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 8).TemplateID),1,this.Nowputongzhanling + 8,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 8).Count);
            PositionUtils.setPos(this.putongzhanlingCell9,"putongzhanling.pos9");
         }
         else if(this.putongzhanlingCell9 != null)
         {
            removeChild(this.putongzhanlingCell9);
         }
         if(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 9) != null)
         {
            this.putongzhanlingCell10 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getputongzhanlingByLevel(FromSpot + 9).TemplateID),1,this.Nowputongzhanling + 9,zhanlingManager.Instance.getputongzhanlingByLevel(this.Nowputongzhanling + 9).Count);
            PositionUtils.setPos(this.putongzhanlingCell10,"putongzhanling.pos10");
         }
         else if(this.putongzhanlingCell10 != null)
         {
            removeChild(this.putongzhanlingCell10);
         }
         if(this.putongzhanlingCell1 != null)
         {
            addChild(this.putongzhanlingCell1);
         }
         if(this.putongzhanlingCell2 != null)
         {
            addChild(this.putongzhanlingCell2);
         }
         if(this.putongzhanlingCell3 != null)
         {
            addChild(this.putongzhanlingCell3);
         }
         if(this.putongzhanlingCell4 != null)
         {
            addChild(this.putongzhanlingCell4);
         }
         if(this.putongzhanlingCell5 != null)
         {
            addChild(this.putongzhanlingCell5);
         }
         if(this.putongzhanlingCell6 != null)
         {
            addChild(this.putongzhanlingCell6);
         }
         if(this.putongzhanlingCell7 != null)
         {
            addChild(this.putongzhanlingCell7);
         }
         if(this.putongzhanlingCell8 != null)
         {
            addChild(this.putongzhanlingCell8);
         }
         if(this.putongzhanlingCell9 != null)
         {
            addChild(this.putongzhanlingCell9);
         }
         if(this.putongzhanlingCell10 != null)
         {
            addChild(this.putongzhanlingCell10);
         }
      }
      
      private function Refreshgaojizhanling(FromSpot:int) : void
      {
         this.disposegaojizhanlingCell();
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot) != null)
         {
            this.gaojizhanlingCell1 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot).TemplateID),2,this.Nowgaojizhanling + 0,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 0).Count);
            PositionUtils.setPos(this.gaojizhanlingCell1,"gaojizhanling.pos1");
         }
         else if(this.gaojizhanlingCell1 != null)
         {
            removeChild(this.gaojizhanlingCell1);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 1) != null)
         {
            this.gaojizhanlingCell2 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 1).TemplateID),2,this.Nowgaojizhanling + 1,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 1).Count);
            PositionUtils.setPos(this.gaojizhanlingCell2,"gaojizhanling.pos2");
         }
         else if(this.gaojizhanlingCell2 != null)
         {
            removeChild(this.gaojizhanlingCell2);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 2) != null)
         {
            this.gaojizhanlingCell3 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 2).TemplateID),2,this.Nowgaojizhanling + 2,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 2).Count);
            PositionUtils.setPos(this.gaojizhanlingCell3,"gaojizhanling.pos3");
         }
         else if(this.gaojizhanlingCell3 != null)
         {
            removeChild(this.gaojizhanlingCell3);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 3) != null)
         {
            this.gaojizhanlingCell4 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 3).TemplateID),2,this.Nowgaojizhanling + 3,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 3).Count);
            PositionUtils.setPos(this.gaojizhanlingCell4,"gaojizhanling.pos4");
         }
         else if(this.gaojizhanlingCell4 != null)
         {
            removeChild(this.gaojizhanlingCell4);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 4) != null)
         {
            this.gaojizhanlingCell5 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 4).TemplateID),2,this.Nowgaojizhanling + 4,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 4).Count);
            PositionUtils.setPos(this.gaojizhanlingCell5,"gaojizhanling.pos5");
         }
         else if(this.gaojizhanlingCell5 != null)
         {
            removeChild(this.gaojizhanlingCell5);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 5) != null)
         {
            this.gaojizhanlingCell6 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 5).TemplateID),2,this.Nowgaojizhanling + 5,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 5).Count);
            PositionUtils.setPos(this.gaojizhanlingCell6,"gaojizhanling.pos6");
         }
         else if(this.gaojizhanlingCell6 != null)
         {
            removeChild(this.gaojizhanlingCell6);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 6) != null)
         {
            this.gaojizhanlingCell7 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 6).TemplateID),2,this.Nowgaojizhanling + 6,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 6).Count);
            PositionUtils.setPos(this.gaojizhanlingCell7,"gaojizhanling.pos7");
         }
         else if(this.gaojizhanlingCell7 != null)
         {
            removeChild(this.gaojizhanlingCell7);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 7) != null)
         {
            this.gaojizhanlingCell8 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 7).TemplateID),2,this.Nowgaojizhanling + 7,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 7).Count);
            PositionUtils.setPos(this.gaojizhanlingCell8,"gaojizhanling.pos8");
         }
         else if(this.gaojizhanlingCell8 != null)
         {
            removeChild(this.gaojizhanlingCell8);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 8) != null)
         {
            this.gaojizhanlingCell9 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 8).TemplateID),2,this.Nowgaojizhanling + 8,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 8).Count);
            PositionUtils.setPos(this.gaojizhanlingCell9,"gaojizhanling.pos9");
         }
         else if(this.gaojizhanlingCell9 != null)
         {
            removeChild(this.gaojizhanlingCell9);
         }
         if(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 9) != null)
         {
            this.gaojizhanlingCell10 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getgaojizhanlingByLevel(FromSpot + 9).TemplateID),2,this.Nowgaojizhanling + 9,zhanlingManager.Instance.getgaojizhanlingByLevel(this.Nowgaojizhanling + 9).Count);
            PositionUtils.setPos(this.gaojizhanlingCell10,"gaojizhanling.pos10");
         }
         else if(this.gaojizhanlingCell10 != null)
         {
            removeChild(this.gaojizhanlingCell10);
         }
         if(this.gaojizhanlingCell1 != null)
         {
            addChild(this.gaojizhanlingCell1);
         }
         if(this.gaojizhanlingCell2 != null)
         {
            addChild(this.gaojizhanlingCell2);
         }
         if(this.gaojizhanlingCell3 != null)
         {
            addChild(this.gaojizhanlingCell3);
         }
         if(this.gaojizhanlingCell4 != null)
         {
            addChild(this.gaojizhanlingCell4);
         }
         if(this.gaojizhanlingCell5 != null)
         {
            addChild(this.gaojizhanlingCell5);
         }
         if(this.gaojizhanlingCell6 != null)
         {
            addChild(this.gaojizhanlingCell6);
         }
         if(this.gaojizhanlingCell7 != null)
         {
            addChild(this.gaojizhanlingCell7);
         }
         if(this.gaojizhanlingCell8 != null)
         {
            addChild(this.gaojizhanlingCell8);
         }
         if(this.gaojizhanlingCell9 != null)
         {
            addChild(this.gaojizhanlingCell9);
         }
         if(this.gaojizhanlingCell10 != null)
         {
            addChild(this.gaojizhanlingCell10);
         }
      }
      
      private function Refreshchaojizhanling(FromSpot:int) : void
      {
         this.disposechaojizhanlingCell();
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot) != null)
         {
            this.chaojizhanlingCell1 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot).TemplateID),3,this.Nowchaojizhanling + 0,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 0).Count);
            PositionUtils.setPos(this.chaojizhanlingCell1,"chaojizhanling.pos1");
         }
         else if(this.chaojizhanlingCell1 != null)
         {
            removeChild(this.chaojizhanlingCell1);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 1) != null)
         {
            this.chaojizhanlingCell2 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 1).TemplateID),3,this.Nowchaojizhanling + 1,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 1).Count);
            PositionUtils.setPos(this.chaojizhanlingCell2,"chaojizhanling.pos2");
         }
         else if(this.chaojizhanlingCell2 != null)
         {
            removeChild(this.chaojizhanlingCell2);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 2) != null)
         {
            this.chaojizhanlingCell3 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 2).TemplateID),3,this.Nowchaojizhanling + 2,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 2).Count);
            PositionUtils.setPos(this.chaojizhanlingCell3,"chaojizhanling.pos3");
         }
         else if(this.chaojizhanlingCell3 != null)
         {
            removeChild(this.chaojizhanlingCell3);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 3) != null)
         {
            this.chaojizhanlingCell4 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 3).TemplateID),3,this.Nowchaojizhanling + 3,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 3).Count);
            PositionUtils.setPos(this.chaojizhanlingCell4,"chaojizhanling.pos4");
         }
         else if(this.chaojizhanlingCell4 != null)
         {
            removeChild(this.chaojizhanlingCell4);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 4) != null)
         {
            this.chaojizhanlingCell5 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 4).TemplateID),3,this.Nowchaojizhanling + 4,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 4).Count);
            PositionUtils.setPos(this.chaojizhanlingCell5,"chaojizhanling.pos5");
         }
         else if(this.chaojizhanlingCell5 != null)
         {
            removeChild(this.chaojizhanlingCell5);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 5) != null)
         {
            this.chaojizhanlingCell6 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 5).TemplateID),3,this.Nowchaojizhanling + 5,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 5).Count);
            PositionUtils.setPos(this.chaojizhanlingCell6,"chaojizhanling.pos6");
         }
         else if(this.chaojizhanlingCell6 != null)
         {
            removeChild(this.chaojizhanlingCell6);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 6) != null)
         {
            this.chaojizhanlingCell7 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 6).TemplateID),3,this.Nowchaojizhanling + 6,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 6).Count);
            PositionUtils.setPos(this.chaojizhanlingCell7,"chaojizhanling.pos7");
         }
         else if(this.chaojizhanlingCell7 != null)
         {
            removeChild(this.chaojizhanlingCell7);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 7) != null)
         {
            this.chaojizhanlingCell8 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 7).TemplateID),3,this.Nowchaojizhanling + 7,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 7).Count);
            PositionUtils.setPos(this.chaojizhanlingCell8,"chaojizhanling.pos8");
         }
         else if(this.chaojizhanlingCell8 != null)
         {
            removeChild(this.chaojizhanlingCell8);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 8) != null)
         {
            this.chaojizhanlingCell9 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 8).TemplateID),3,this.Nowchaojizhanling + 8,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 8).Count);
            PositionUtils.setPos(this.chaojizhanlingCell9,"chaojizhanling.pos9");
         }
         else if(this.chaojizhanlingCell9 != null)
         {
            removeChild(this.chaojizhanlingCell9);
         }
         if(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 9) != null)
         {
            this.chaojizhanlingCell10 = new zhanlingItemCell(ItemManager.Instance.getTemplateById(zhanlingManager.Instance.getchaojizhanlingByLevel(FromSpot + 9).TemplateID),3,this.Nowchaojizhanling + 9,zhanlingManager.Instance.getchaojizhanlingByLevel(this.Nowchaojizhanling + 9).Count);
            PositionUtils.setPos(this.chaojizhanlingCell10,"chaojizhanling.pos10");
         }
         else if(this.chaojizhanlingCell10 != null)
         {
            removeChild(this.chaojizhanlingCell10);
         }
         if(this.chaojizhanlingCell1 != null)
         {
            addChild(this.chaojizhanlingCell1);
         }
         if(this.chaojizhanlingCell2 != null)
         {
            addChild(this.chaojizhanlingCell2);
         }
         if(this.chaojizhanlingCell3 != null)
         {
            addChild(this.chaojizhanlingCell3);
         }
         if(this.chaojizhanlingCell4 != null)
         {
            addChild(this.chaojizhanlingCell4);
         }
         if(this.chaojizhanlingCell5 != null)
         {
            addChild(this.chaojizhanlingCell5);
         }
         if(this.chaojizhanlingCell6 != null)
         {
            addChild(this.chaojizhanlingCell6);
         }
         if(this.chaojizhanlingCell7 != null)
         {
            addChild(this.chaojizhanlingCell7);
         }
         if(this.chaojizhanlingCell8 != null)
         {
            addChild(this.chaojizhanlingCell8);
         }
         if(this.chaojizhanlingCell9 != null)
         {
            addChild(this.chaojizhanlingCell9);
         }
         if(this.chaojizhanlingCell10 != null)
         {
            addChild(this.chaojizhanlingCell10);
         }
      }
      
      private function initEvent() : void
      {
         this._putongleftBtn.addEventListener(MouseEvent.CLICK,this.__putongzhanlingleft);
         this._putongrightBtn.addEventListener(MouseEvent.CLICK,this.__putongzhanlingright);
         this._gaojileftBtn.addEventListener(MouseEvent.CLICK,this._gaojizhanlingleft);
         this._gaojirightBtn.addEventListener(MouseEvent.CLICK,this._gaojizhanlingright);
         this._chaojileftBtn.addEventListener(MouseEvent.CLICK,this._chaojizhanlingleft);
         this._chaojirightBtn.addEventListener(MouseEvent.CLICK,this._chaojizhanlingright);
         this._ZhanlingIcon.addEventListener(MouseEvent.CLICK,this._ZhanlingIconClick);
      }
      
      protected function __frameEvent(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.playButtonSound();
         var _local_2:Disposeable = _arg_1.target as Disposeable;
         _local_2.dispose();
         _local_2 = null;
      }
      
      private function _ZhanlingIconClick(_arg_1:MouseEvent) : void
      {
         var s1:String = null;
         if(PlayerManager.Instance.Self.zhanlingVipType == 0)
         {
            s1 = LanguageMgr.GetTranslation("zhanling.tips1") + "普通战令";
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 1)
         {
            s1 = LanguageMgr.GetTranslation("zhanling.tips2") + "高级战令";
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 2)
         {
            s1 = LanguageMgr.GetTranslation("zhanling.tips3") + "超级战令";
         }
         MessageTipManager.getInstance().show(s1);
      }
      
      private function __putongzhanlingleft(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowputongzhanling == 1)
         {
            MessageTipManager.getInstance().show("已到达首页");
         }
         else
         {
            this.Nowputongzhanling -= 1;
            this.Refreshputongzhanling(this.Nowputongzhanling);
         }
      }
      
      private function __putongzhanlingright(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowputongzhanling + 9 >= zhanlingManager.putongzhanlingMaxLevel)
         {
            MessageTipManager.getInstance().show("已到达末页");
         }
         else
         {
            this.Nowputongzhanling += 1;
            this.Refreshputongzhanling(this.Nowputongzhanling);
         }
      }
      
      private function _gaojizhanlingleft(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowgaojizhanling == 1)
         {
            MessageTipManager.getInstance().show("已到达首页");
         }
         else
         {
            this.Nowgaojizhanling -= 1;
            this.Refreshgaojizhanling(this.Nowgaojizhanling);
         }
      }
      
      private function _gaojizhanlingright(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowgaojizhanling + 9 >= zhanlingManager.gaojizhanlingMaxLevel)
         {
            MessageTipManager.getInstance().show("已到达末页");
         }
         else
         {
            this.Nowgaojizhanling += 1;
            this.Refreshgaojizhanling(this.Nowgaojizhanling);
         }
      }
      
      private function _chaojizhanlingleft(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowchaojizhanling == 1)
         {
            MessageTipManager.getInstance().show("已到达首页");
         }
         else
         {
            this.Nowchaojizhanling -= 1;
            this.Refreshchaojizhanling(this.Nowchaojizhanling);
         }
      }
      
      private function _chaojizhanlingright(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this.Nowchaojizhanling + 9 >= zhanlingManager.chaojizhanlingMaxLevel)
         {
            MessageTipManager.getInstance().show("已到达末页");
         }
         else
         {
            this.Nowchaojizhanling += 1;
            this.Refreshchaojizhanling(this.Nowchaojizhanling);
         }
      }
      
      private function removeEvent() : void
      {
      }
      
      private function disposeputongzhanlingCell() : void
      {
         if(this.putongzhanlingCell1 != null)
         {
            removeChild(this.putongzhanlingCell1);
         }
         if(this.putongzhanlingCell2 != null)
         {
            removeChild(this.putongzhanlingCell2);
         }
         if(this.putongzhanlingCell3 != null)
         {
            removeChild(this.putongzhanlingCell3);
         }
         if(this.putongzhanlingCell4 != null)
         {
            removeChild(this.putongzhanlingCell4);
         }
         if(this.putongzhanlingCell5 != null)
         {
            removeChild(this.putongzhanlingCell5);
         }
         if(this.putongzhanlingCell6 != null)
         {
            removeChild(this.putongzhanlingCell6);
         }
         if(this.putongzhanlingCell7 != null)
         {
            removeChild(this.putongzhanlingCell7);
         }
         if(this.putongzhanlingCell8 != null)
         {
            removeChild(this.putongzhanlingCell8);
         }
         if(this.putongzhanlingCell9 != null)
         {
            removeChild(this.putongzhanlingCell9);
         }
         if(this.putongzhanlingCell10 != null)
         {
            removeChild(this.putongzhanlingCell10);
         }
      }
      
      private function disposegaojizhanlingCell() : void
      {
         if(this.gaojizhanlingCell1 != null)
         {
            removeChild(this.gaojizhanlingCell1);
         }
         if(this.gaojizhanlingCell2 != null)
         {
            removeChild(this.gaojizhanlingCell2);
         }
         if(this.gaojizhanlingCell3 != null)
         {
            removeChild(this.gaojizhanlingCell3);
         }
         if(this.gaojizhanlingCell4 != null)
         {
            removeChild(this.gaojizhanlingCell4);
         }
         if(this.gaojizhanlingCell5 != null)
         {
            removeChild(this.gaojizhanlingCell5);
         }
         if(this.gaojizhanlingCell6 != null)
         {
            removeChild(this.gaojizhanlingCell6);
         }
         if(this.gaojizhanlingCell7 != null)
         {
            removeChild(this.gaojizhanlingCell7);
         }
         if(this.gaojizhanlingCell8 != null)
         {
            removeChild(this.gaojizhanlingCell8);
         }
         if(this.gaojizhanlingCell9 != null)
         {
            removeChild(this.gaojizhanlingCell9);
         }
         if(this.gaojizhanlingCell10 != null)
         {
            removeChild(this.gaojizhanlingCell10);
         }
      }
      
      private function disposechaojizhanlingCell() : void
      {
         if(this.chaojizhanlingCell1 != null)
         {
            removeChild(this.chaojizhanlingCell1);
         }
         if(this.chaojizhanlingCell2 != null)
         {
            removeChild(this.chaojizhanlingCell2);
         }
         if(this.chaojizhanlingCell3 != null)
         {
            removeChild(this.chaojizhanlingCell3);
         }
         if(this.chaojizhanlingCell4 != null)
         {
            removeChild(this.chaojizhanlingCell4);
         }
         if(this.chaojizhanlingCell5 != null)
         {
            removeChild(this.chaojizhanlingCell5);
         }
         if(this.chaojizhanlingCell6 != null)
         {
            removeChild(this.chaojizhanlingCell6);
         }
         if(this.chaojizhanlingCell7 != null)
         {
            removeChild(this.chaojizhanlingCell7);
         }
         if(this.chaojizhanlingCell8 != null)
         {
            removeChild(this.chaojizhanlingCell8);
         }
         if(this.chaojizhanlingCell9 != null)
         {
            removeChild(this.chaojizhanlingCell9);
         }
         if(this.chaojizhanlingCell10 != null)
         {
            removeChild(this.chaojizhanlingCell10);
         }
      }
      
      public function dispose() : void
      {
         SocketManager.Instance.removeEventListener("Update_Zhanling",this.__updateAuction);
         this.removeEvent();
         ObjectUtils.disposeAllChildren(this);
         this._bg = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      private function __updateAuction(_arg_1:CrazyTankSocketEvent) : void
      {
         var s1:String = null;
         var _local_1:int = _arg_1.pkg.readInt();
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:int = _arg_1.pkg.readInt();
         var _local_4:int = _arg_1.pkg.readInt();
         if(this._goldTxt != null)
         {
            this._goldTxt.text = _local_1.toString();
         }
         if(this._ZhanlingExpTxt != null)
         {
            this._ZhanlingExpTxt.text = "当前经验:" + _local_2.toString() + "/100";
         }
         if(this._ZhanlingLevel != null)
         {
            this._ZhanlingLevel.text = "当前等级:" + _local_3.toString();
         }
         if(this._ZhanlingIcon != null)
         {
            if(_local_4 == 0)
            {
               s1 = "zhanling.chujizhanling.Icon";
            }
            if(_local_4 == 1)
            {
               s1 = "zhanling.gaojizhanling.Icon";
            }
            if(_local_4 == 2)
            {
               s1 = "zhanling.chaojizhanling.Icon";
            }
            this._ZhanlingIcon = null;
            this._ZhanlingIcon = ComponentFactory.Instance.creatComponentByStylename(s1);
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 0)
         {
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 1)
         {
         }
         if(PlayerManager.Instance.Self.zhanlingVipType == 2)
         {
         }
         addChild(this._ZhanlingIcon);
         this.Refreshputongzhanling(this.Nowputongzhanling);
         this.Refreshgaojizhanling(this.Nowgaojizhanling);
         this.Refreshchaojizhanling(this.Nowchaojizhanling);
      }
   }
}

